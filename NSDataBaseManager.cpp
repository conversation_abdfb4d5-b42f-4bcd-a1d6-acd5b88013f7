#include "NSDataBaseManager.h"
#include "Connection/NSMySQLConnectionPool.h"
#include "NSIOCPWorkManager.h"
#include "StoredProcedure/NSStoredProcedure.h"
#include "NSDataSerializer.h"
#include "NSQueryData.h"
#include "Storage/NSStorageUpdateContainer.h"
#include "Storage/NSStorageManager.h"
#include "NSDBSession.h"
#include <thread>
#include <sstream>

NSDataBaseManager::NSDataBaseManager()
{
}

NSDataBaseManager::~NSDataBaseManager()
{
    Finalize();
}

bool NSDataBaseManager::Initialize()
{
    if (m_initialized.exchange(true))
        return true;

    try
    {
        // IOCP 워커 매니저 생성
        m_workerManager = std::make_unique<NSIOCPWorkManager>();
        
        // 연결 풀 생성 (아직 초기화하지 않음)
        for (int i = 0; i < 12; ++i)
        {
            m_connectionPools[i] = std::make_unique<NSMySQLConnectionPool>();
        }

        return true;
    }
    catch (const std::exception& e)
    {
        m_initialized = false;
        return false;
    }
}

void NSDataBaseManager::Finalize()
{
    if (!m_initialized.exchange(false))
        return;

    Stop();

    // 연결 풀 종료
    for (auto& pool : m_connectionPools)
    {
        if (pool)
        {
            pool->Finalize();
            pool.reset();
        }
    }
}

bool NSDataBaseManager::AddConnectionInfo(int32 dbType, int32 shardId, const std::string& host, int port,
                                         const std::string& dbName, const std::string& user, const std::string& password)
{
    auto* pool = GetConnectionPool(dbType, shardId);
    if (!pool)
        return false;
        
    return pool->AddConnectionInfo(host, port, dbName, user, password);
}

NSMySQLConnectionPool* NSDataBaseManager::GetDBConnection(int32 dbType, int32 shardId)
{
    return GetConnectionPool(dbType, shardId);
}

void NSDataBaseManager::ReconnectConnection(int32 dbType, int32 shardId)
{
    auto* pool = GetConnectionPool(dbType, shardId);
    if (pool)
    {
        pool->Reconnect();
    }
}

int64_t NSDataBaseManager::GetDBQueueSize() const
{
    if (!m_workerManager)
        return 0;
    return m_workerManager->GetQueueSize();
}

std::string NSDataBaseManager::GetConnectionPoolCountInfo() const
{
    std::stringstream ss;
    for (int i = 0; i < 12; ++i)
    {
        if (m_connectionPools[i])
        {
            ss << "Pool[" << i << "]: " << m_connectionPools[i]->GetActiveConnectionCount() 
               << "/" << m_connectionPools[i]->GetTotalConnectionCount() << " ";
        }
    }
    return ss.str();
}

std::string NSDataBaseManager::GetConnectionPoolCountLog() const
{
    return GetConnectionPoolCountInfo(); // 동일한 정보 반환
}

NSMySQLConnectionPool* NSDataBaseManager::GetConnectionPool(int32 dbType, int32 shardId)
{
    int poolIndex = -1;

    switch (dbType)
    {
    case 0: // GameDB
        if (shardId >= 0 && shardId < 10)
            poolIndex = shardId;
        break;
    case 1: // CommonDB
        poolIndex = 10;
        break;
    case 2: // LogDB
        poolIndex = 11;
        break;
    }

    if (poolIndex >= 0 && poolIndex < 12)
        return m_connectionPools[poolIndex].get();

    return nullptr;
}

// 시퀀스 관리 메서드 구현 (단순화)
int64_t NSDataBaseManager::GetNextStorageSequence(int64_t cid)
{
    // atomic이므로 lock 불필요
    return ++m_sequenceByCid[cid];
}

void NSDataBaseManager::OnSessionClosed(int64_t cid)
{
    std::unique_lock lock(m_sequenceMutex);
    m_sequenceByCid.erase(cid);
}

template<typename SP>
DBPromise<std::shared_ptr<NSQueryData>> NSDataBaseManager::StartQueryImpl(
    const NS::Connection& connection,
    const NSDataSerializer& serializer,
    int64_t transactionId)
{
    // Access 체크
    if (m_pushAccessProhibit.load())
    {
        return DBPromise<std::shared_ptr<NSQueryData>>::CreateRejected(
            std::make_exception_ptr(std::runtime_error("Push access prohibited")));
    }
    
    m_pushAccessCount++;
    
    return DBPromise<std::shared_ptr<NSQueryData>>::Create([=](auto promise) {
        // 연결 풀 가져오기
        auto* pool = GetConnectionPool(connection.DatabaseType, connection.ShardId);
        if (!pool)
        {
            m_pushAccessCount--;
            promise.SetException(std::make_exception_ptr(
                std::runtime_error("Invalid connection pool")));
            return;
        }

        // 샤드키 기반 스레드 선택
        uint64_t shardKey = 0;
        if constexpr (requires { SP::Input; })
        {
            NSDataSerializer tempSerializer = serializer;
            SP::Input input;
            tempSerializer >> input;
            if constexpr (requires { input.Cid; })
                shardKey = input.Cid;
            else if constexpr (requires { input.Aid; })
                shardKey = input.Aid;
        }
        
        int threadIndex = GetExecutorByShardKey(shardKey);
        
        m_queriesProcessing++;
        m_inputCount++;
        
        // IOCP 워커에서 비동기 실행
        m_workerManager->QueueWork([=]() mutable
        {
            auto queryData = std::make_shared<NSQueryData>();
            
            try
            {
                // 연결 획듍
                auto conn = pool->GetConnection();
                if (!conn)
                {
                    throw std::runtime_error("Failed to get connection");
                }

                // 저장 프로시저 실행
                SP sp;
                auto result = sp.Execute(conn.get(), serializer);
                queryData->SetErrorCode(result);
                
                m_outputCount++;
                
                // 콜백 실행
                if (m_afterExecuteQuery)
                    m_afterExecuteQuery(*queryData);

                // 결과 반환
                promise.SetValue(queryData);

                // 연결 반환
                pool->ReturnConnection(std::move(conn));
            }
            catch (...)
            {
                promise.SetException(std::current_exception());
            }
            
            m_queriesProcessing--;
            m_pushAccessCount--;
        }, threadIndex);
    });
}

bool NSDataBaseManager::Start(uint32_t workThreadCnt)
{
    if (!m_initialized)
        return false;
        
    // 스레드 수 결정
    if (workThreadCnt == 0)
        workThreadCnt = std::thread::hardware_concurrency() * 2 + 16;
    
    m_threadCount = workThreadCnt;
    
    // IOCP 워커 매니저 시작
    if (!m_workerManager->Initialize(workThreadCnt))
        return false;
        
    // 연결 풀 초기화
    // GameDB 0-9 (10개)
    for (int i = 0; i < 10; ++i)
    {
        if (!m_connectionPools[i]->Initialize(0, i)) // DatabaseType=0, ShardId=i
            return false;
    }

    // CommonDB (인덱스 10)
    if (!m_connectionPools[10]->Initialize(1, 0)) // DatabaseType=1, ShardId=0
        return false;

    // LogDB (인덱스 11)
    if (!m_connectionPools[11]->Initialize(2, 0)) // DatabaseType=2, ShardId=0
        return false;
        
    return true;
}

void NSDataBaseManager::Stop()
{
    ProhibitPushAccess();
    StopAllWorkerThreadAndWait();
}

void NSDataBaseManager::ProhibitPushAccess()
{
    m_pushAccessProhibit = true;
}

void NSDataBaseManager::StopAllWorkerThreadAndWait()
{
    // 모든 큐 작업이 완료될 때까지 대기
    while (m_pushAccessCount > 0 || m_queriesProcessing > 0)
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    // 워커 매니저 종료
    if (m_workerManager)
    {
        m_workerManager->Finalize();
    }
}

DBPromise<std::shared_ptr<NSQueryData>> NSDataBaseManager::StorageUpdateQuery(
    std::shared_ptr<NSStorageUpdateContainer> containerData,
    std::function<EErrorCode(const std::shared_ptr<NSQueryData>&, const std::shared_ptr<NSStorageUpdateContainer>&)> pQueryFunc,
    std::function<EErrorCode(const std::shared_ptr<NSQueryData>&, const std::shared_ptr<NSStorageUpdateContainer>&)> pResultFunc,
    std::shared_ptr<NSDBSession> session,
    std::source_location location)
{
    // Access 체크
    if (m_pushAccessProhibit.load())
    {
        return DBPromise<std::shared_ptr<NSQueryData>>::CreateRejected(
            std::make_exception_ptr(std::runtime_error("Push access prohibited")));
    }
    
    m_pushAccessCount++;
    
    return DBPromise<std::shared_ptr<NSQueryData>>::Create([=](auto promise) {
        auto queryData = std::make_shared<NSQueryData>(location.function_name(), location.line(), session);
        
        // CID 기반 스레드 선택
        int threadIndex = GetExecutorByShardKey(containerData->Cid);
        
        m_queriesProcessing++;
        
        // 작업 큐에 추가
        m_workerManager->QueueWork([=]() mutable
        {
            try {
                // Query 실행
                EErrorCode queryResult = EErrorCode::None;
                if (pQueryFunc)
                {
                    queryResult = pQueryFunc(queryData, containerData);
                    queryData->SetErrorCode(queryResult);
                }
                
                // Result 처리
                if (pResultFunc)
                {
                    queryResult = pResultFunc(queryData, containerData);
                    queryData->SetErrorCode(queryResult);
                }
                
                // 콜백 실행
                if (m_afterExecuteQuery)
                    m_afterExecuteQuery(*queryData);
                
                promise.SetValue(queryData);
            }
            catch (...) {
                promise.SetException(std::current_exception());
            }
            
            m_queriesProcessing--;
            m_pushAccessCount--;
        }, threadIndex);
    });
}