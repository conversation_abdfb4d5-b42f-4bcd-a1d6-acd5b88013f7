#pragma once

// mimalloc 통합
#ifdef USE_MIMALLOC

#ifdef _MSC_VER
#pragma comment(lib, "mimalloc-static.lib")
#endif

#include <mimalloc.h>

// 전역 new/delete 오버라이드
inline void* operator new(size_t size) 
{
    return mi_malloc(size);
}

inline void* operator new[](size_t size) 
{
    return mi_malloc(size);
}

inline void operator delete(void* ptr) noexcept 
{
    mi_free(ptr);
}

inline void operator delete[](void* ptr) noexcept 
{
    mi_free(ptr);
}

inline void operator delete(void* ptr, size_t) noexcept 
{
    mi_free(ptr);
}

inline void operator delete[](void* ptr, size_t) noexcept 
{
    mi_free(ptr);
}

// STL 컨테이너용 커스텀 할당자
template<typename T>
class MimallocAllocator 
{
public:
    using value_type = T;
    using size_type = std::size_t;
    using difference_type = std::ptrdiff_t;

    MimallocAllocator() = default;

    template<typename U>
    MimallocAllocator(const MimallocAllocator<U>&) noexcept {}

    T* allocate(size_type n) 
    {
        return static_cast<T*>(mi_malloc(n * sizeof(T)));
    }

    void deallocate(T* ptr, size_type) noexcept 
    {
        mi_free(ptr);
    }

    template<typename U>
    bool operator==(const MimallocAllocator<U>&) const noexcept 
    {
        return true;
    }

    template<typename U>
    bool operator!=(const MimallocAllocator<U>&) const noexcept 
    {
        return false;
    }
};

// mimalloc 사용하는 문자열 타입
using MimallocString = std::basic_string<char, std::char_traits<char>, MimallocAllocator<char>>;

// mimalloc 사용하는 벡터 타입
template<typename T>
using MimallocVector = std::vector<T, MimallocAllocator<T>>;

// mimalloc 사용하는 맵 타입
template<typename K, typename V>
using MimallocMap = std::unordered_map<K, V, std::hash<K>, std::equal_to<K>, 
                                       MimallocAllocator<std::pair<const K, V>>>;

#endif // USE_MIMALLOC