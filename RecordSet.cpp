#include "stdafx.h"
#include "RecordSet.h"
#include <cstring>

RecordSet::RecordSet(MYSQL* mysql, MYSQL_RES* result)
    : m_mysql(mysql), m_result(result)
{
    if (m_result)
    {
        m_fieldCount = mysql_num_fields(m_result);
        InitFieldMapping();
    }
}

RecordSet::RecordSet(MYSQL_STMT* stmt, MYSQL_RES* metadata)
    : m_stmt(stmt), m_result(metadata)
{
    if (m_result && m_stmt)
    {
        m_fieldCount = mysql_num_fields(m_result);
        InitFieldMapping();
        
        // Statement 바인딩 준비
        m_binds = std::make_unique<MYSQL_BIND[]>(m_fieldCount);
        m_bindBuffer = std::make_unique<char[]>(m_fieldCount * 1024); // 각 필드당 1KB
        m_lengths = std::make_unique<unsigned long[]>(m_fieldCount);
        m_isNull = std::make_unique<my_bool[]>(m_fieldCount);
        
        memset(m_binds.get(), 0, sizeof(MYSQL_BIND) * m_fieldCount);
        
        char* bufferPtr = m_bindBuffer.get();
        MYSQL_FIELD* fields = mysql_fetch_fields(m_result);
        
        for (int i = 0; i < m_fieldCount; ++i)
        {
            m_binds[i].buffer_type = fields[i].type;
            m_binds[i].buffer = bufferPtr;
            m_binds[i].buffer_length = 1024;
            m_binds[i].length = &m_lengths[i];
            m_binds[i].is_null = &m_isNull[i];
            
            bufferPtr += 1024;
        }
        
        mysql_stmt_bind_result(m_stmt, m_binds.get());
    }
}

RecordSet::~RecordSet()
{
    // 결과는 Connection에서 관리하므로 여기서는 해제하지 않음
}

bool RecordSet::IsEOF()
{
    // 첫 호출 시 첫 번째 행으로 이동
    if (m_firstCall)
    {
        m_firstCall = false;
        return !MoveNext();
    }
    
    // 이미 EOF인 경우
    if (m_isEof)
        return true;
    
    // 다음 행으로 자동 이동
    return !MoveNext();
}

bool RecordSet::MoveNext()
{
    if (m_stmt)
    {
        // PreparedStatement인 경우
        int fetchResult = mysql_stmt_fetch(m_stmt);
        if (fetchResult == 0)
        {
            return true;
        }
        else if (fetchResult == MYSQL_NO_DATA)
        {
            m_isEof = true;
            return false;
        }
        else
        {
            // 에러 처리
            m_isEof = true;
            return false;
        }
    }
    else if (m_mysql && m_result)
    {
        // 일반 쿼리인 경우
        m_currentRow = mysql_fetch_row(m_result);
        if (m_currentRow)
        {
            return true;
        }
        else
        {
            m_isEof = true;
            return false;
        }
    }
    
    m_isEof = true;
    return false;
}

bool RecordSet::GetItem(const std::string& fieldName, int32_t& value)
{
    int index = GetFieldIndex(fieldName);
    if (index < 0)
        return false;
    return GetItem(index, value);
}

bool RecordSet::GetItem(const std::string& fieldName, int64_t& value)
{
    int index = GetFieldIndex(fieldName);
    if (index < 0)
        return false;
    return GetItem(index, value);
}

bool RecordSet::GetItem(const std::string& fieldName, float& value)
{
    int index = GetFieldIndex(fieldName);
    if (index < 0)
        return false;
    return GetItem(index, value);
}

bool RecordSet::GetItem(const std::string& fieldName, double& value)
{
    int index = GetFieldIndex(fieldName);
    if (index < 0)
        return false;
    return GetItem(index, value);
}

bool RecordSet::GetItem(const std::string& fieldName, std::string& value)
{
    int index = GetFieldIndex(fieldName);
    if (index < 0)
        return false;
    return GetItem(index, value);
}

bool RecordSet::GetItem(const std::string& fieldName, std::vector<uint8_t>& value)
{
    int index = GetFieldIndex(fieldName);
    if (index < 0)
        return false;
    return GetItem(index, value);
}

bool RecordSet::GetItem(int fieldIndex, int32_t& value)
{
    if (fieldIndex < 0 || fieldIndex >= m_fieldCount)
        return false;
    
    if (m_stmt)
    {
        if (m_isNull[fieldIndex])
            return false;
        
        // 타입에 따라 변환
        MYSQL_FIELD* fields = mysql_fetch_fields(m_result);
        switch (fields[fieldIndex].type)
        {
        case MYSQL_TYPE_TINY:
        case MYSQL_TYPE_SHORT:
        case MYSQL_TYPE_LONG:
        case MYSQL_TYPE_INT24:
            value = *reinterpret_cast<int32_t*>(m_binds[fieldIndex].buffer);
            return true;
        case MYSQL_TYPE_LONGLONG:
            value = static_cast<int32_t>(*reinterpret_cast<int64_t*>(m_binds[fieldIndex].buffer));
            return true;
        default:
            return false;
        }
    }
    else if (m_currentRow)
    {
        if (!m_currentRow[fieldIndex])
            return false;
        
        value = std::stoi(m_currentRow[fieldIndex]);
        return true;
    }
    
    return false;
}

bool RecordSet::GetItem(int fieldIndex, int64_t& value)
{
    if (fieldIndex < 0 || fieldIndex >= m_fieldCount)
        return false;
    
    if (m_stmt)
    {
        if (m_isNull[fieldIndex])
            return false;
        
        MYSQL_FIELD* fields = mysql_fetch_fields(m_result);
        switch (fields[fieldIndex].type)
        {
        case MYSQL_TYPE_LONGLONG:
            value = *reinterpret_cast<int64_t*>(m_binds[fieldIndex].buffer);
            return true;
        case MYSQL_TYPE_TINY:
        case MYSQL_TYPE_SHORT:
        case MYSQL_TYPE_LONG:
        case MYSQL_TYPE_INT24:
            value = static_cast<int64_t>(*reinterpret_cast<int32_t*>(m_binds[fieldIndex].buffer));
            return true;
        default:
            return false;
        }
    }
    else if (m_currentRow)
    {
        if (!m_currentRow[fieldIndex])
            return false;
        
        value = std::stoll(m_currentRow[fieldIndex]);
        return true;
    }
    
    return false;
}

bool RecordSet::GetItem(int fieldIndex, float& value)
{
    if (fieldIndex < 0 || fieldIndex >= m_fieldCount)
        return false;
    
    if (m_stmt)
    {
        if (m_isNull[fieldIndex])
            return false;
        
        MYSQL_FIELD* fields = mysql_fetch_fields(m_result);
        if (fields[fieldIndex].type == MYSQL_TYPE_FLOAT)
        {
            value = *reinterpret_cast<float*>(m_binds[fieldIndex].buffer);
            return true;
        }
    }
    else if (m_currentRow)
    {
        if (!m_currentRow[fieldIndex])
            return false;
        
        value = std::stof(m_currentRow[fieldIndex]);
        return true;
    }
    
    return false;
}

bool RecordSet::GetItem(int fieldIndex, double& value)
{
    if (fieldIndex < 0 || fieldIndex >= m_fieldCount)
        return false;
    
    if (m_stmt)
    {
        if (m_isNull[fieldIndex])
            return false;
        
        MYSQL_FIELD* fields = mysql_fetch_fields(m_result);
        if (fields[fieldIndex].type == MYSQL_TYPE_DOUBLE)
        {
            value = *reinterpret_cast<double*>(m_binds[fieldIndex].buffer);
            return true;
        }
    }
    else if (m_currentRow)
    {
        if (!m_currentRow[fieldIndex])
            return false;
        
        value = std::stod(m_currentRow[fieldIndex]);
        return true;
    }
    
    return false;
}

bool RecordSet::GetItem(int fieldIndex, std::string& value)
{
    if (fieldIndex < 0 || fieldIndex >= m_fieldCount)
        return false;
    
    if (m_stmt)
    {
        if (m_isNull[fieldIndex])
            return false;
        
        value.assign(reinterpret_cast<char*>(m_binds[fieldIndex].buffer), m_lengths[fieldIndex]);
        return true;
    }
    else if (m_currentRow)
    {
        if (!m_currentRow[fieldIndex])
            return false;
        
        value = m_currentRow[fieldIndex];
        return true;
    }
    
    return false;
}

bool RecordSet::GetItem(int fieldIndex, std::vector<uint8_t>& value)
{
    if (fieldIndex < 0 || fieldIndex >= m_fieldCount)
        return false;
    
    if (m_stmt)
    {
        if (m_isNull[fieldIndex])
            return false;
        
        uint8_t* data = reinterpret_cast<uint8_t*>(m_binds[fieldIndex].buffer);
        value.assign(data, data + m_lengths[fieldIndex]);
        return true;
    }
    else if (m_currentRow)
    {
        if (!m_currentRow[fieldIndex])
            return false;
        
        unsigned long* lengths = mysql_fetch_lengths(m_result);
        if (lengths)
        {
            uint8_t* data = reinterpret_cast<uint8_t*>(m_currentRow[fieldIndex]);
            value.assign(data, data + lengths[fieldIndex]);
            return true;
        }
    }
    
    return false;
}

const char* RecordSet::GetFieldName(int index) const
{
    if (!m_result || index < 0 || index >= m_fieldCount)
        return nullptr;
    
    MYSQL_FIELD* fields = mysql_fetch_fields(m_result);
    return fields[index].name;
}

int RecordSet::GetFieldIndex(const std::string& fieldName)
{
    auto it = m_fieldMapping.find(fieldName);
    if (it != m_fieldMapping.end())
        return it->second;
    return -1;
}

void RecordSet::InitFieldMapping()
{
    if (!m_result)
        return;
    
    MYSQL_FIELD* fields = mysql_fetch_fields(m_result);
    for (int i = 0; i < m_fieldCount; ++i)
    {
        m_fieldMapping[fields[i].name] = i;
    }
}