#pragma once
#include <memory>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <thread>
#include <atomic>

// Forward declaration
class NSMySQLConnection;

// 심플한 MySQL 연결 풀 (스레드 로컬 캐싱 지원)
class NSMySQLConnectionPool
{
public:
    NSMySQLConnectionPool();
    ~NSMySQLConnectionPool();

    // 초기화/종료
    bool Initialize(int databaseType, int shardId);
    void Finalize();

    // 연결 획득/반환
    std::unique_ptr<NSMySQLConnection> GetConnection();
    void ReturnConnection(std::unique_ptr<NSMySQLConnection> conn);

    // 설정
    void SetMinConnections(int count) { m_minConnections = count; }
    void SetMaxConnections(int count) { m_maxConnections = count; }

private:
    // 연결 생성
    std::unique_ptr<NSMySQLConnection> CreateConnection();
    
    // 연결 정보 로드
    bool LoadConnectionInfo();

    // 스레드 로컬 연결 캐시
    static thread_local std::unique_ptr<NSMySQLConnection> t_localConnection;

private:
    // 연결 풀
    std::queue<std::unique_ptr<NSMySQLConnection>> m_connections;
    std::mutex m_mutex;
    std::condition_variable m_cv;

    // 설정
    int m_databaseType = 0;
    int m_shardId = 0;
    int m_minConnections = 5;
    int m_maxConnections = 20;
    
    // 현재 연결 수
    std::atomic<int> m_currentConnections{0};
    
    // 연결 정보
    std::string m_host;
    int m_port = 3306;
    std::string m_user;
    std::string m_password;
    std::string m_database;
    
    bool m_initialized = false;
};