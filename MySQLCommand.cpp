#include "stdafx.h"
#include "MySQLCommand.h"

MySQLCommand::MySQLCommand()
{
}

MySQLCommand::~MySQLCommand()
{
}

bool MySQLCommand::GetItem(const std::string& name, int32_t& value)
{
    auto it = m_outputParameters.find(name);
    if (it == m_outputParameters.end())
        return false;
    
    if (auto* v = std::get_if<int32_t>(&it->second))
    {
        value = *v;
        return true;
    }
    return false;
}

bool MySQLCommand::GetItem(const std::string& name, int64_t& value)
{
    auto it = m_outputParameters.find(name);
    if (it == m_outputParameters.end())
        return false;
    
    if (auto* v = std::get_if<int64_t>(&it->second))
    {
        value = *v;
        return true;
    }
    return false;
}

bool MySQLCommand::GetItem(const std::string& name, float& value)
{
    auto it = m_outputParameters.find(name);
    if (it == m_outputParameters.end())
        return false;
    
    if (auto* v = std::get_if<float>(&it->second))
    {
        value = *v;
        return true;
    }
    return false;
}

bool MySQLCommand::GetItem(const std::string& name, double& value)
{
    auto it = m_outputParameters.find(name);
    if (it == m_outputParameters.end())
        return false;
    
    if (auto* v = std::get_if<double>(&it->second))
    {
        value = *v;
        return true;
    }
    return false;
}

bool MySQLCommand::GetItem(const std::string& name, std::string& value)
{
    auto it = m_outputParameters.find(name);
    if (it == m_outputParameters.end())
        return false;
    
    if (auto* v = std::get_if<std::string>(&it->second))
    {
        value = *v;
        return true;
    }
    return false;
}

bool MySQLCommand::GetItem(const std::string& name, std::vector<uint8_t>& value)
{
    auto it = m_outputParameters.find(name);
    if (it == m_outputParameters.end())
        return false;
    
    if (auto* v = std::get_if<std::vector<uint8_t>>(&it->second))
    {
        value = *v;
        return true;
    }
    return false;
}

void MySQLCommand::BindToMySQL(MYSQL_STMT* stmt, std::vector<MYSQL_BIND>& binds)
{
    // MySQL 파라미터 바인딩 구현
    // 프로시저 메타데이터에 따라 순서대로 바인딩
    size_t index = 0;
    for (auto& bind : binds)
    {
        memset(&bind, 0, sizeof(MYSQL_BIND));
        
        // 파라미터 이름으로 값 찾기 (실제로는 메타데이터 순서대로)
        // 여기서는 간단하게 구현
        if (index < m_parameters.size())
        {
            auto it = m_parameters.begin();
            std::advance(it, index);
            
            std::visit([&bind](auto&& arg) {
                using T = std::decay_t<decltype(arg)>;
                if constexpr (std::is_same_v<T, int32_t>)
                {
                    bind.buffer_type = MYSQL_TYPE_LONG;
                    bind.buffer = (void*)&arg;
                    bind.is_unsigned = 0;
                }
                else if constexpr (std::is_same_v<T, int64_t>)
                {
                    bind.buffer_type = MYSQL_TYPE_LONGLONG;
                    bind.buffer = (void*)&arg;
                    bind.is_unsigned = 0;
                }
                else if constexpr (std::is_same_v<T, float>)
                {
                    bind.buffer_type = MYSQL_TYPE_FLOAT;
                    bind.buffer = (void*)&arg;
                }
                else if constexpr (std::is_same_v<T, double>)
                {
                    bind.buffer_type = MYSQL_TYPE_DOUBLE;
                    bind.buffer = (void*)&arg;
                }
                else if constexpr (std::is_same_v<T, std::string>)
                {
                    bind.buffer_type = MYSQL_TYPE_VAR_STRING;
                    bind.buffer = (void*)arg.data();
                    bind.buffer_length = arg.length();
                    bind.length = nullptr;
                }
                else if constexpr (std::is_same_v<T, std::vector<uint8_t>>)
                {
                    bind.buffer_type = MYSQL_TYPE_BLOB;
                    bind.buffer = (void*)arg.data();
                    bind.buffer_length = arg.size();
                    bind.length = nullptr;
                }
            }, it->second);
        }
        index++;
    }
    
    if (!binds.empty())
    {
        mysql_stmt_bind_param(stmt, binds.data());
    }
}

void MySQLCommand::RetrieveOutputParameters(MYSQL_STMT* stmt)
{
    // OUT 파라미터 가져오기
    // 실제 구현에서는 프로시저 메타데이터를 참조하여
    // OUT/INOUT 파라미터만 가져와야 함
    
    // 여기서는 간단히 구현
    m_outputParameters = m_parameters; // 임시로 복사
}